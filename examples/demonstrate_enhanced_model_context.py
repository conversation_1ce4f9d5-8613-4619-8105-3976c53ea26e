"""
Demonstration script showing the enhanced model context functionality in endpoint extraction.

This script shows how the pipeline now passes rich model context (not just model names) 
to the endpoint extraction process, providing better experimental context for endpoint interpretation.
"""

import json
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from src.utils.extraction_pydantic_models import ExperimentalModel
from jinja2 import Template


def demonstrate_enhanced_model_context():
    """Demonstrate the enhanced model context functionality."""
    
    print("=" * 80)
    print("DEMONSTRATION: Enhanced Model Context for Endpoint Extraction")
    print("=" * 80)
    print()
    
    print("PROBLEM SOLVED:")
    print("Previously, only basic model names (e.g., 'SK-BR-3') were passed to endpoint extraction,")
    print("losing crucial experimental context like genetic modifications, resistance patterns, etc.")
    print()
    
    # Example models with rich experimental context
    models = [
        ExperimentalModel(
            citations=[
                "SK-BR-3 cells were transfected with HER2 ECD protein to achieve 10-fold overexpression.",
                "The HER2-overexpressing SK-BR-3 variant showed enhanced binding to trastuzumab."
            ],
            model_name="SK-BR-3-HER2-overexpressing",
            clinical_human_related=False,
            cancer_type="Breast Cancer",
            cancer_subtype="HER2-positive breast cancer",
            investigative=True,
            reasoning_for_inclusion="This genetically modified SK-BR-3 variant was selected for its extremely high HER2 expression levels (10-fold higher than parental), achieved through transfection with HER2 ECD protein, making it ideal for evaluating maximum binding capacity and cytotoxic potential of HER2-targeting ADCs."
        ),
        ExperimentalModel(
            citations=[
                "JIMT-1 cells are naturally resistant to trastuzumab due to HER2 masking.",
                "This cell line represents trastuzumab-resistant HER2-positive breast cancer."
            ],
            model_name="JIMT-1",
            clinical_human_related=False,
            cancer_type="Breast Cancer",
            cancer_subtype="Trastuzumab-resistant HER2-positive breast cancer",
            investigative=True,
            reasoning_for_inclusion="JIMT-1 was chosen to evaluate ADC efficacy in trastuzumab-resistant settings, representing a critical clinical challenge where conventional HER2-targeted therapies fail due to HER2 masking mechanisms."
        ),
        ExperimentalModel(
            citations=[
                "BT-474 cells express moderate levels of HER2 and are sensitive to HER2-targeted therapy.",
                "This model represents the typical HER2-positive breast cancer patient population."
            ],
            model_name="BT-474",
            clinical_human_related=False,
            cancer_type="Breast Cancer",
            cancer_subtype="HER2-positive breast cancer",
            investigative=True,
            reasoning_for_inclusion="BT-474 serves as a standard HER2-positive control model with moderate HER2 expression levels, representing the typical patient population and providing a baseline for comparing ADC efficacy across different HER2 expression levels."
        )
    ]
    
    print("EXAMPLE MODELS WITH RICH CONTEXT:")
    print("-" * 50)
    for i, model in enumerate(models, 1):
        print(f"{i}. {model.model_name}")
        print(f"   Cancer Subtype: {model.cancer_subtype}")
        print(f"   Key Context: {model.reasoning_for_inclusion[:100]}...")
        print()
    
    # Simulate the enhanced context creation from the pipeline
    print("ENHANCED CONTEXT CREATION (as done in the pipeline):")
    print("-" * 50)
    
    model_contexts = []
    model_names = []
    
    for model in models:
        model_names.append(model.model_name)
        
        # Build comprehensive model context (same logic as in extract_endpoints)
        context_parts = [f"Model Name: {model.model_name}"]
        
        if model.cancer_type and model.cancer_type != "NONE":
            context_parts.append(f"Cancer Type: {model.cancer_type}")
            
        if model.cancer_subtype:
            context_parts.append(f"Cancer Subtype: {model.cancer_subtype}")
            
        if model.reasoning_for_inclusion:
            context_parts.append(f"Model Selection Rationale: {model.reasoning_for_inclusion}")
            
        # Include key citations that might contain experimental modifications
        if model.citations:
            key_citations = model.citations[:2]
            context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
        
        model_contexts.append(" | ".join(context_parts))
    
    print("Model Names (old approach):")
    for name in model_names:
        print(f"  - {name}")
    print()
    
    print("Enhanced Model Contexts (new approach):")
    for i, context in enumerate(model_contexts, 1):
        print(f"{i}. {context}")
        print()
    
    # Show how this appears in the endpoint extraction prompt
    print("ENDPOINT EXTRACTION PROMPT ENHANCEMENT:")
    print("-" * 50)
    
    # Sample prompt template (simplified version)
    prompt_template = """
ADC Name: Trastuzumab-DM1

Endpoint Name: ADC_IC50

The models to analyse:
{{MODELS}}

Detailed Model Context and Characteristics:
{% for context in MODEL_CONTEXTS %}
- {{context}}
{% endfor %}

ENHANCED CONTEXT AWARENESS: Use the detailed model context provided above to better understand:
- Specific genetic modifications or expressions (e.g., "HER2-overexpressing", "transfected with")
- Model variants and experimental characteristics
- The rationale behind model selection and its relevance to the endpoint measurements
- Cancer type and subtype specificity that may affect endpoint interpretation
- Any experimental modifications that could influence the measured values

When extracting IC50 measurements, consider how the specific model characteristics affect interpretation:
- HER2-overexpressing models may show enhanced sensitivity
- Trastuzumab-resistant models may require higher concentrations
- Standard models provide baseline comparisons
"""
    
    template = Template(prompt_template)
    rendered_prompt = template.render(
        MODELS=model_names,
        MODEL_CONTEXTS=model_contexts
    )
    
    print("Sample rendered prompt:")
    print(rendered_prompt)
    
    print("=" * 80)
    print("BENEFITS OF ENHANCED MODEL CONTEXT:")
    print("=" * 80)
    print()
    
    benefits = [
        "🎯 PRECISE INTERPRETATION: Endpoint measurements can be interpreted in the context of specific model characteristics",
        "🧬 GENETIC CONTEXT: Genetic modifications (e.g., HER2 overexpression) are preserved and available",
        "🔬 EXPERIMENTAL AWARENESS: Understanding of model variants and experimental conditions",
        "📊 BETTER VALIDATION: Model selection rationale helps validate endpoint relevance",
        "🏥 CLINICAL RELEVANCE: Cancer subtypes and resistance patterns inform clinical translation",
        "🔍 ENHANCED ACCURACY: Reduced risk of misinterpreting endpoint results due to missing context",
        "📝 COMPREHENSIVE CITATIONS: Key experimental details from citations are preserved",
        "⚖️ COMPARATIVE ANALYSIS: Better understanding of why different models show different results"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    print()
    
    print("SPECIFIC EXAMPLES OF IMPROVED INTERPRETATION:")
    print("-" * 50)
    
    examples = [
        {
            "scenario": "IC50 Measurement Interpretation",
            "old_approach": "IC50 = 0.1 nM for SK-BR-3 (just the name)",
            "new_approach": "IC50 = 0.1 nM for SK-BR-3-HER2-overexpressing (10-fold HER2 overexpression)",
            "benefit": "Now we understand this low IC50 is due to extremely high target expression"
        },
        {
            "scenario": "Resistance Pattern Understanding", 
            "old_approach": "IC50 = 10 nM for JIMT-1 (just the name)",
            "new_approach": "IC50 = 10 nM for JIMT-1 (trastuzumab-resistant due to HER2 masking)",
            "benefit": "Higher IC50 is explained by known resistance mechanism, not poor ADC design"
        },
        {
            "scenario": "Baseline Comparison",
            "old_approach": "IC50 = 1 nM for BT-474 (just the name)", 
            "new_approach": "IC50 = 1 nM for BT-474 (standard HER2+ model, moderate expression)",
            "benefit": "Provides proper baseline for comparing other model variants"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['scenario']}:")
        print(f"   Old: {example['old_approach']}")
        print(f"   New: {example['new_approach']}")
        print(f"   💡 {example['benefit']}")
        print()
    
    print("=" * 80)
    print("IMPLEMENTATION SUMMARY:")
    print("=" * 80)
    print()
    
    implementation_points = [
        "✅ Modified extract_endpoints() function to create rich model contexts",
        "✅ Updated endpoint extraction prompt to utilize MODEL_CONTEXTS",
        "✅ Enhanced system prompt to emphasize context-aware interpretation", 
        "✅ Preserved all existing functionality while adding rich context",
        "✅ Implemented citation length management (first 2 citations)",
        "✅ Added comprehensive test coverage for context creation",
        "✅ Maintained backward compatibility with existing data structures"
    ]
    
    for point in implementation_points:
        print(f"  {point}")
    print()
    
    print("The enhanced model context ensures that endpoint extraction agents have access")
    print("to the full experimental context needed for accurate interpretation and extraction")
    print("of endpoint measurements, solving the critical problem of lost experimental context.")
    print()
    print("=" * 80)
    print("Demonstration complete!")
    print("=" * 80)


if __name__ == "__main__":
    demonstrate_enhanced_model_context()
