"""
Scalable processing of domain annotations and test data filtering.

This script:
1. Processes training CSV with expert annotations into structured JSON for few-shot prompting
2. Filters large test dataset based on current training endpoints and exclusions
3. Dynamically adapts as training annotations grow over time
4. Maintains clean train/test separation
"""

import pandas as pd
import numpy as np
import json
import sys
import os
import argparse

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_csv_annotations(csv_path):
    """Load and clean the annotated CSV file."""
    print(f"Loading annotations from: {csv_path}")
    
    # Read CSV, skipping the first header row and handle encoding issues
    df = pd.read_csv(csv_path, skiprows=1, encoding='latin-1')
    print(f"Loaded {len(df)} rows from CSV")
    
    # Clean column names (remove any extra spaces)
    df.columns = df.columns.str.strip()
    
    # Filter out rows with missing essential data
    essential_cols = ['paper_id', 'extraction_id', 'endpoint_name', 'new_human_classification', 'support_span']
    df_clean = df.dropna(subset=essential_cols)
    print(f"After cleaning: {len(df_clean)} rows with complete data")
    
    return df_clean

def safe_get(row, column, default=''):
    """Helper function to handle NaN values safely."""
    value = row.get(column, default)
    return '' if pd.isna(value) or (isinstance(value, float) and np.isnan(value)) else str(value).strip()

def structure_training_annotations(df):
    """Structure all training annotations as few-shot examples (no train/test split)."""
    endpoints = df['endpoint_name'].unique()
    print(f"Found training endpoints: {list(endpoints)}")
    
    structured_data = {}
    
    for endpoint in endpoints:
        endpoint_data = df[df['endpoint_name'] == endpoint].copy()
        print(f"\nProcessing {endpoint}: {len(endpoint_data)} training examples")
        
        examples = []
        for _, row in endpoint_data.iterrows():
            example = {
                "extraction": {
                    "id": row['extraction_id'],
                    "paper_id": row['paper_id'],
                    "adc_name": safe_get(row, 'adc_name'),
                    "model_name": safe_get(row, 'model_name'),
                    "model_type": safe_get(row, 'model_type'),
                    "experiment_type": safe_get(row, 'experiment_type'),
                    "endpoint_name": row['endpoint_name'],
                    "measured_value": safe_get(row, 'measured_value'),
                    "measured_dose": safe_get(row, 'measured_dose'),
                    "measured_concentrations": safe_get(row, 'measured_concentrations'),
                    "measured_timepoint": safe_get(row, 'measured_timepoint'),
                    "measured_death_percentage": safe_get(row, 'measured_death_percentage'),
                    "endpoint_citations": row['support_span']
                },
                "label": row['new_human_classification'].strip(),  # TP or FP
                "reasoning": safe_get(row, 'human_reasoning')
            }
            examples.append(example)
        
        # All examples are training data (few-shot prompts)
        structured_data[endpoint] = {
            "train": examples,  # All examples for few-shot prompting
            "total_examples": len(examples)
        }
        
        print(f"  - Training examples: {len(examples)}")
        
        # Show label distribution
        tp_count = sum(1 for ex in examples if ex['label'] == 'TP')
        fp_count = sum(1 for ex in examples if ex['label'] == 'FP')
        print(f"  - Label distribution: {tp_count} TP, {fp_count} FP")
    
    return structured_data, endpoints

def load_and_filter_test_data(test_xlsx_path, training_endpoints, training_extraction_ids):
    """Load and filter the large test dataset from Excel file."""
    print(f"\nLoading test data from Excel file: {test_xlsx_path}")
    
    # Read Excel file - specifically the "Endpoints" sheet, first row contains column names
    df = pd.read_excel(test_xlsx_path, sheet_name='Endpoints')
    print(f"Loaded {len(df)} test rows from Excel file (Endpoints sheet)")
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # No filtering - include all rows and all endpoints
    df_filtered = df.copy()
    print(f"Including all data (no filtering): {len(df_filtered)} rows")
    
    # Show endpoint distribution in test data
    endpoint_counts = df_filtered['endpoint_name'].value_counts()
    print("\nTest data distribution:")
    for endpoint, count in endpoint_counts.items():
        print(f"  - {endpoint}: {count} test examples")
    
    return df_filtered

def save_filtered_test_data(df_filtered, output_path):
    """Save the filtered test dataset."""
    print(f"\nSaving filtered test data to: {output_path}")
    df_filtered.to_csv(output_path, index=False, encoding='utf-8')
    print("Filtered test data saved successfully!")

def save_structured_data(structured_data, output_path):
    """Save the structured training data to JSON file."""
    print(f"\nSaving structured training data to: {output_path}")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(structured_data, f, indent=2, ensure_ascii=False)
    
    print("Structured training data saved successfully!")
    
    # Print summary
    total_examples = sum(data['total_examples'] for data in structured_data.values())
    total_train = sum(len(data['train']) for data in structured_data.values())
    
    print(f"\nTraining Data Summary:")
    print(f"- Total examples: {total_examples}")
    print(f"- Training examples: {total_train}")
    print(f"- Endpoints: {len(structured_data)}")

def main(test_file=None):
    """Main processing function for scalable train/test data preparation."""
    # File paths
    training_csv_path = "live_evaluation/04_AZ_ADC_LiveEvaluation_iter1_results(Few-shot examples) (3).csv"
    
    # Use provided test file or default (test files are always Excel)
    if test_file:
        test_xlsx_path = test_file
    else:
        test_xlsx_path = "extraction_100_itr3.xlsx"
    
    # Output paths - automatically generate based on test file name
    test_file_base = os.path.splitext(os.path.basename(test_xlsx_path))[0]
    structured_training_path = "live_evaluation/structured_annotations_full.json"
    
    # Output is always CSV regardless of input format
    filtered_test_path = f"filtered_{test_file_base}.csv"
    
    # Check if input files exist
    for path in [training_csv_path, test_xlsx_path]:
        if not os.path.exists(path):
            print(f"ERROR: Input file not found: {path}")
            return False
    
    try:
        print("=== SCALABLE DOMAIN ANNOTATION PROCESSING ===")
        
        # Process training data
        print("\n[1/3] Processing training annotations...")
        df_training = load_csv_annotations(training_csv_path)
        structured_data, training_endpoints = structure_training_annotations(df_training)
        
        # Get training extraction IDs for exclusion
        training_extraction_ids = set(df_training['extraction_id'])
        print(f"Training extraction IDs to exclude: {len(training_extraction_ids)}")
        
        # Process and filter test data
        print("\n[2/3] Processing and filtering test data...")
        df_test_filtered = load_and_filter_test_data(
            test_xlsx_path, 
            training_endpoints, 
            training_extraction_ids
        )
        
        # Save outputs
        print("\n[3/3] Saving processed data...")
        save_structured_data(structured_data, structured_training_path)
        save_filtered_test_data(df_test_filtered, filtered_test_path)
        
        print("\n=== PROCESSING COMPLETE ===")
        print(f"Training endpoints: {list(training_endpoints)}")
        print(f"Training examples: {len(df_training)}")
        print(f"Test examples (after filtering): {len(df_test_filtered)}")
        
        return True
        
    except Exception as e:
        print(f"ERROR during processing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Process domain annotations and filter test data')
    parser.add_argument('--test-file', type=str, help='Path to test Excel file (.xlsx). Default: extraction_100_itr3.xlsx')
    
    args = parser.parse_args()
    
    success = main(test_file=args.test_file)
    sys.exit(0 if success else 1)