"""
Parallel Domain Expert Citation Checker with Expanded Scope

This script implements fast parallel citation checking with:
1. All endpoints evaluation (not filtered by training data)
2. Parallel processing with configurable workers 
3. Random few-shot examples for untrained endpoints
4. Shuffled evaluation order for balanced processing
5. Progress logging after 10 evaluations, then every 100
"""

import pandas as pd
import numpy as np
import json
import sys
import os
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import time
import argparse
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openai import AzureOpenAI
from dotenv import load_dotenv
load_dotenv()

class CitationCheckResult(BaseModel):
    """Structured output format for citation checking."""
    classification: str  # "TP" or "FP" 
    reasoning: str

class ParallelDomainExpertCitationChecker:
    """Fast parallel citation checker with expanded scope."""
    
    def __init__(self, annotations_path, parallel_workers=5):
        """Initialize the parallel citation checker."""
        self.client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version="2024-10-01-preview", 
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
        )
        
        self.model = "gpt-4.1"  # Using GPT-4.1 as specified
        self.annotations_path = annotations_path
        self.parallel_workers = parallel_workers
        self.annotations = {}
        self.fallback_examples = {"tp": None, "fp": None}
        self.progress_lock = Lock()
        self.evaluation_count = 0
        self.endpoint_results = {}
        
        self.load_annotations()
        self.prepare_fallback_examples()
    
    def load_annotations(self):
        """Load structured domain annotations."""
        with open(self.annotations_path, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
    
    def prepare_fallback_examples(self):
        """Prepare random TP and FP examples for untrained endpoints."""
        all_tp_examples = []
        all_fp_examples = []
        
        # Collect all TP and FP examples from training data
        for endpoint_data in self.annotations.values():
            for example in endpoint_data['train']:
                if example['label'] == 'TP':
                    all_tp_examples.append(example)
                elif example['label'] == 'FP':
                    all_fp_examples.append(example)
        
        # Randomly select one of each
        if all_tp_examples and all_fp_examples:
            self.fallback_examples['tp'] = random.choice(all_tp_examples)
            self.fallback_examples['fp'] = random.choice(all_fp_examples)
            
            print(f"Selected fallback examples for untrained endpoints:")
            print(f"  - TP: {self.fallback_examples['tp']['extraction']['id']}")
            print(f"  - FP: {self.fallback_examples['fp']['extraction']['id']}")
    
    def safe_get(self, row, column, default=''):
        """Helper to safely get values from DataFrame rows."""
        value = row.get(column, default)
        return '' if pd.isna(value) or (isinstance(value, float) and np.isnan(value)) else str(value).strip()
    
    def calculate_agreement_score(self, predictions, true_labels):
        """Calculate agreement score using (agreements - disagreements) / total formula."""
        if len(predictions) != len(true_labels) or len(predictions) == 0:
            return 0.0
        
        agreements = sum(1 for p, t in zip(predictions, true_labels) if p == t)
        disagreements = sum(1 for p, t in zip(predictions, true_labels) if p != t)
        total = len(predictions)
        
        agreement_score = (agreements - disagreements) / total
        return agreement_score

    def build_prompt_with_examples(self, extraction: Dict, few_shot_examples: List[Dict] = None):
        """Build prompt with few-shot examples using system/user message structure."""
        
        # System prompt
        system_prompt = """You are evaluating whether a citation supports an extracted endpoint from an ADC research paper.

Your task is to determine if the citation text provides sufficient evidence to support the extracted endpoint information.

Classification Guidelines:
- TP (True Positive): The citation clearly and directly supports all aspects of the endpoint
- FP (False Positive): The citation does not adequately support the endpoint, or there are discrepancies

Consider:
1. Does the citation mention the specific ADC, model, and endpoint?
2. Are the measured values consistent between extraction and citation?
3. Are the experimental conditions (model_type, experiment_type) accurately reflected?
4. Is the endpoint_name appropriately assigned based on what was actually measured?"""

        # Add few-shot examples if provided
        examples_section = ""
        if few_shot_examples:
            examples_section = "\n\nExamples:\n"
            for i, example in enumerate(few_shot_examples, 1):
                ex_extraction = example['extraction']
                
                # Build example info with conditional fields
                example_info = []
                example_info.append(f"ADC_Name: {ex_extraction['adc_name']}")
                example_info.append(f"Model_Name: {ex_extraction['model_name']}")
                example_info.append(f"Model_Type: {ex_extraction['model_type']}")
                example_info.append(f"Experiment_Type: {ex_extraction['experiment_type']}")
                example_info.append(f"Endpoint_Name: {ex_extraction['endpoint_name']}")
                example_info.append(f"Measured_Value: {ex_extraction['measured_value']}")
                
                # Add conditional measurement fields only if not empty
                if ex_extraction.get('measured_dose', ''):
                    example_info.append(f"Measured_Dose: {ex_extraction['measured_dose']}")
                if ex_extraction.get('measured_concentrations', ''):
                    example_info.append(f"Measured_Concentrations: {ex_extraction['measured_concentrations']}")
                if ex_extraction.get('measured_timepoint', ''):
                    example_info.append(f"Measured_Timepoint: {ex_extraction['measured_timepoint']}")
                if ex_extraction.get('measured_death_percentage', ''):
                    example_info.append(f"Measured_Death_Percentage: {ex_extraction['measured_death_percentage']}")
                
                # NO TEXT TRUNCATION - use complete citations and reasoning
                examples_section += f"""
Example {i}:
{chr(10).join(example_info)}

Citation: {ex_extraction['endpoint_citations']}

Classification: {example['label']}
Reasoning: {example['reasoning']}
"""
        
        # Current evaluation task
        current_info = []
        current_info.append(f"- ADC_Name: {extraction['adc_name']}")
        current_info.append(f"- Model_Name: {extraction['model_name']}")
        current_info.append(f"- Model_Type: {extraction['model_type']}")
        current_info.append(f"- Experiment_Type: {extraction['experiment_type']}")
        current_info.append(f"- Endpoint_Name: {extraction['endpoint_name']}")
        current_info.append(f"- Measured_Value: {extraction['measured_value']}")
        
        # Add conditional measurement fields only if not empty
        if extraction.get('measured_dose', ''):
            current_info.append(f"- Measured_Dose: {extraction['measured_dose']}")
        if extraction.get('measured_concentrations', ''):
            current_info.append(f"- Measured_Concentrations: {extraction['measured_concentrations']}")
        if extraction.get('measured_timepoint', ''):
            current_info.append(f"- Measured_Timepoint: {extraction['measured_timepoint']}")
        if extraction.get('measured_death_percentage', ''):
            current_info.append(f"- Measured_Death_Percentage: {extraction['measured_death_percentage']}")
        
        task_section = f"""
{examples_section}

Now evaluate this extraction:

Endpoint Information:
{chr(10).join(current_info)}

Citation Text:
{extraction['endpoint_citations']}

Classify as TP or FP and provide your reasoning."""
        
        return system_prompt, task_section
    
    def check_citation(self, extraction: Dict, few_shot_examples: List[Dict] = None):
        """Check citation with optional few-shot examples."""
        
        system_prompt, task_section = self.build_prompt_with_examples(extraction, few_shot_examples)
        
        try:
            response = self.client.beta.chat.completions.parse(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": task_section}
                ],
                response_format=CitationCheckResult,
                temperature=0,
                timeout=30
            )
            
            return response.choices[0].message.parsed
            
        except Exception as e:
            print(f"Error checking citation for {extraction['id']}: {str(e)}")
            return CitationCheckResult(
                classification="FP",
                reasoning=f"Error during processing: {str(e)}"
            )
    
    def evaluate_single_extraction(self, extraction_data):
        """Evaluate a single extraction with appropriate few-shot examples."""
        row, index = extraction_data
        
        # Convert row to extraction format
        extraction = {
            "id": row['id'],
            "adc_name": self.safe_get(row, 'adc_name'),
            "model_name": self.safe_get(row, 'model_name'),
            "model_type": self.safe_get(row, 'model_type'),
            "experiment_type": self.safe_get(row, 'experiment_type'),
            "endpoint_name": row['endpoint_name'],
            "measured_value": self.safe_get(row, 'measured_value'),
            "measured_dose": self.safe_get(row, 'measured_dose'),
            "measured_concentrations": self.safe_get(row, 'measured_concentration'),
            "measured_timepoint": self.safe_get(row, 'measured_timepoint'),
            "measured_death_percentage": self.safe_get(row, 'measured_death_percentage'),
            "endpoint_citations": row['endpoint_citations']
        }
        
        endpoint = extraction['endpoint_name']
        true_label = row.get('human_classification', 'TP').strip() if pd.notna(row.get('human_classification')) else 'TP'
        
        # Determine few-shot examples to use
        if endpoint in self.annotations:
            # Use all training examples for this endpoint
            few_shot_examples = self.annotations[endpoint]['train']
        else:
            # Use fallback examples (1 TP, 1 FP) for untrained endpoints
            few_shot_examples = [self.fallback_examples['tp'], self.fallback_examples['fp']]
        
        # Run citation check with few-shot examples
        result = self.check_citation(extraction, few_shot_examples=few_shot_examples)
        predicted_label = result.classification
        
        # Update progress and log periodically
        with self.progress_lock:
            self.evaluation_count += 1
            
            # Update per-endpoint tracking
            if endpoint not in self.endpoint_results:
                self.endpoint_results[endpoint] = {'predictions': [], 'true_labels': []}
            
            self.endpoint_results[endpoint]['predictions'].append(predicted_label)
            self.endpoint_results[endpoint]['true_labels'].append(true_label)
            
            # Log progress at specified intervals
            if self.evaluation_count == 10 or (self.evaluation_count >= 100 and self.evaluation_count % 100 == 0):
                print(f"\n=== Progress after {self.evaluation_count} evaluations ===")
                for ep, data in sorted(self.endpoint_results.items()):
                    if len(data['predictions']) > 0:
                        agreement = self.calculate_agreement_score(data['predictions'], data['true_labels'])
                        print(f"{ep}: {agreement:.2f}")
        
        return {
            "extraction_id": extraction['id'],
            "endpoint_name": endpoint,
            "true_label": true_label,
            "predicted_label": predicted_label,
            "reasoning": result.reasoning,
            "correct": true_label == predicted_label,
            "index": index
        }

    def run_parallel_evaluation(self, test_data_path):
        """Run parallel few-shot evaluation on expanded test dataset."""
        print("\n=== PARALLEL EVALUATION: Few-Shot with Expanded Scope ===")
        
        # Load test data (now includes all endpoints)
        test_df = pd.read_csv(test_data_path)
        print(f"Loaded {len(test_df)} test examples")
        
        # Show endpoint distribution
        endpoint_counts = test_df['endpoint_name'].value_counts()
        print(f"\nTest data includes {len(endpoint_counts)} different endpoints:")
        
        trained_endpoints = []
        untrained_endpoints = []
        for endpoint, count in endpoint_counts.items():
            if endpoint in self.annotations:
                trained_endpoints.append(endpoint)
                train_count = len(self.annotations[endpoint]['train'])
                print(f"  - {endpoint}: {count} test examples (trained with {train_count} examples)")
            else:
                untrained_endpoints.append(endpoint)
                print(f"  - {endpoint}: {count} test examples (untrained - using fallback examples)")
        
        print(f"\nUsing {self.fallback_examples['tp']['extraction']['id']} (TP) and {self.fallback_examples['fp']['extraction']['id']} (FP) as few-shot examples for {len(untrained_endpoints)} untrained endpoints")
        
        # Prepare data for parallel processing - shuffle for random order
        test_data_list = [(row, idx) for idx, (_, row) in enumerate(test_df.iterrows())]
        random.shuffle(test_data_list)
        print(f"\nShuffled {len(test_data_list)} evaluations for parallel processing")
        
        # Run parallel evaluation
        detailed_results = []
        results_by_endpoint = {}
        
        print(f"\nStarting parallel evaluation with {self.parallel_workers} workers...")
        
        with ThreadPoolExecutor(max_workers=self.parallel_workers) as executor:
            # Submit all tasks
            future_to_data = {
                executor.submit(self.evaluate_single_extraction, extraction_data): extraction_data
                for extraction_data in test_data_list
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_data):
                try:
                    result = future.result()
                    detailed_results.append(result)
                    
                    endpoint = result['endpoint_name']
                    if endpoint not in results_by_endpoint:
                        results_by_endpoint[endpoint] = {
                            'predictions': [],
                            'true_labels': [],
                            'test_count': 0
                        }
                    
                    results_by_endpoint[endpoint]['predictions'].append(result['predicted_label'])
                    results_by_endpoint[endpoint]['true_labels'].append(result['true_label'])
                    results_by_endpoint[endpoint]['test_count'] += 1
                    
                except Exception as e:
                    print(f"Error in evaluation: {str(e)}")
        
        # Sort detailed results by original index to maintain order
        detailed_results.sort(key=lambda x: x['index'])
        
        # Calculate endpoint-specific accuracies
        results = {}
        all_predictions = []
        all_true_labels = []
        
        for endpoint, data in results_by_endpoint.items():
            if data['predictions']:
                endpoint_agreement = self.calculate_agreement_score(data['predictions'], data['true_labels'])
                results[endpoint] = {
                    'agreement': endpoint_agreement,
                    'predictions': data['predictions'],
                    'true_labels': data['true_labels'],
                    'test_count': data['test_count']
                }
                
                all_predictions.extend(data['predictions'])
                all_true_labels.extend(data['true_labels'])
        
        # Calculate overall agreement
        if all_predictions:
            overall_agreement = self.calculate_agreement_score(all_predictions, all_true_labels)
            results['overall'] = {
                'agreement': overall_agreement,
                'total_predictions': len(all_predictions)
            }
        
        return results, detailed_results

    def save_results(self, few_shot_results, detailed_results):
        """Save detailed evaluation results in the same format as original."""
        output_path = "domain_expert_evaluation_results_full.json"
        
        # Calculate per-endpoint scores for summary
        few_shot_overall = few_shot_results.get('overall', {}).get('agreement', 0)
        
        # Build per-endpoint agreement data (only few-shot, no baseline)
        few_shot_per_endpoint = {}
        
        all_endpoints = set(few_shot_results.keys())
        all_endpoints.discard('overall')
        
        for endpoint in all_endpoints:
            few_shot_agreement = few_shot_results.get(endpoint, {}).get('agreement', 0)
            
            few_shot_per_endpoint[endpoint] = {
                'agreement': few_shot_agreement,
                'correct': sum(1 for r in detailed_results if r['endpoint_name'] == endpoint and r['correct']),
                'total': sum(1 for r in detailed_results if r['endpoint_name'] == endpoint)
            }
        
        # Prepare results data - simplified format (no baseline iteration)
        results_data = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "summary": {
                "iteration_1_few_shot": {
                    "overall_agreement": few_shot_overall,
                    "total": few_shot_results.get('overall', {}).get('total_predictions', 0),
                    "per_endpoint": few_shot_per_endpoint
                }
            },
            "detailed_results": {
                "iteration_1": detailed_results
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        print(f"\nResults saved to: {output_path}")
        return results_data

    def print_summary(self, few_shot_results):
        """Print evaluation summary."""
        few_shot_agreement = few_shot_results.get('overall', {}).get('agreement', 0)
        total_examples = few_shot_results.get('overall', {}).get('total_predictions', 0)
        
        print(f"\n{'='*50}")
        print("PARALLEL EVALUATION SUMMARY")
        print(f"{'='*50}")
        print(f"Few-shot Agreement Score:   {few_shot_agreement:.3f}")
        print(f"Total Evaluations:          {total_examples}")
        print(f"Target (>0.60):             {'YES' if few_shot_agreement > 0.60 else 'NO'}")
        print(f"Parallel Workers:           {self.parallel_workers}")
        
        print("\nPer-Endpoint Results:")
        all_endpoints = set(few_shot_results.keys())
        all_endpoints.discard('overall')
        
        for endpoint in sorted(all_endpoints):
            few_shot_ep = few_shot_results.get(endpoint, {}).get('agreement', 0)
            test_count = few_shot_results.get(endpoint, {}).get('test_count', 0)
            trained_status = "trained" if endpoint in self.annotations else "untrained"
            print(f"  {endpoint:30}: {few_shot_ep:.3f} ({test_count} examples, {trained_status})")

    def run_evaluations(self, test_data_path):
        """Run parallel few-shot evaluation on expanded test dataset."""
        print("Parallel Domain Expert Citation Checker with Expanded Scope")
        print("="*60)
        print(f"Loading structured annotations from: {self.annotations_path}")
        print(f"Loading test data from: {test_data_path}")
        print(f"Parallel workers: {self.parallel_workers}")
        
        # Show loaded training data summary
        print(f"\nLoaded annotations for {len(self.annotations)} endpoints")
        for endpoint, data in self.annotations.items():
            train_count = len(data['train'])
            print(f"  - {endpoint}: {train_count} training examples")
        
        print("\nStarting parallel evaluations...")
        
        # Run parallel few-shot evaluation
        few_shot_results, detailed_results = self.run_parallel_evaluation(test_data_path)
        
        # Save and summarize results
        self.save_results(few_shot_results, detailed_results)
        self.print_summary(few_shot_results)

def main():
    """Main execution function with command line arguments."""
    parser = argparse.ArgumentParser(description='Parallel Domain Expert Citation Checker')
    parser.add_argument('test_file', type=str, 
                       help='Required: Path to test CSV file (e.g., filtered_extraction_100_itr3.csv)')
    parser.add_argument('--annotations-file', type=str, default='live_evaluation/structured_annotations_full.json',
                       help='Path to annotations JSON file. Default: structured_annotations_full.json')
    parser.add_argument('--parallel', type=int, default=5, 
                       help='Number of parallel workers for evaluation (default: 5)')
    
    args = parser.parse_args()
    
    annotations_path = args.annotations_file
    test_data_path = args.test_file
    
    # Check if files exist
    if not os.path.exists(annotations_path):
        print(f"ERROR: Annotations file not found: {annotations_path}")
        sys.exit(1)
    if not os.path.exists(test_data_path):
        print(f"ERROR: Test file not found: {test_data_path}")
        sys.exit(1)
    
    checker = ParallelDomainExpertCitationChecker(annotations_path, parallel_workers=args.parallel)
    checker.run_evaluations(test_data_path)

if __name__ == "__main__":
    main()