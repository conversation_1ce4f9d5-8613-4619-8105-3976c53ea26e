"""
Cancer term standardizer using OpenAI text-embedding-3-large model.

This module provides functionality to standardize cancer terms using OpenAI's
text-embedding-3-large model and MONDO ontology for semantic similarity matching.
"""

import os
import json
import pickle
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union, Any
from dotenv import load_dotenv
import numpy as np
import faiss

from openai import OpenAI, AzureOpenAI
from oaklib import get_adapter
from oaklib.datamodels.vocabulary import IS_A
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CancerTermStandardizerOpenAI:
    """
    Cancer term standardizer using OpenAI embeddings and MONDO ontology.
    """
    
    def __init__(self, 
                 cache_dir: str = "cache",
                 num_output: int = 10,
                 batch_size: int = 100):
        """
        Initialize the cancer term standardizer.
        
        Args:
            cache_dir: Directory to store cached files
            num_output: Number of matching terms to return
            batch_size: Batch size for OpenAI API calls
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        self.num_output = num_output
        self.batch_size = batch_size
        
        # Initialize OpenAI client
        self.client = self._get_openai_client()
        
        logger.info("Initialized OpenAI cancer term standardizer")

        # File paths for persistent storage
        self.cancer_terms_file = self.cache_dir / 'cancer_terms.pkl'
        self.cancer_embeddings_file = self.cache_dir / 'cancer_embeddings.pkl'
        self.cancer_synonyms_file = self.cache_dir / 'cancer_synonyms.json'

        # MONDO cancer ID
        self.cancer_mondo_id = 'MONDO:0004992'

        # Templates
        self.query_template = "{term} appears in our document in the following context: {context}"
        self.chunk_template = "{term} is defined as {definition}"

        # Initialize cancer terms if not already cached
        self._ensure_cancer_terms_loaded()

    def _get_openai_client(self):
        """Get OpenAI client - automatically detects Azure vs regular OpenAI based on credentials"""
        # Check if Azure credentials are provided
        if os.getenv("AZURE_OPENAI_ENDPOINT") and os.getenv("AZURE_OPENAI_KEY"):
            return AzureOpenAI(
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_KEY"),
                api_version=os.getenv("AZURE_OPENAI_VERSION", "2024-12-01-preview")
            )
        # Fall back to regular OpenAI
        elif os.getenv("OPENAI_API_KEY"):
            return OpenAI(
                api_key=os.getenv("OPENAI_API_KEY")
            )
        else:
            raise ValueError(
                "No OpenAI credentials found. Please provide either:\n"
                "- OPENAI_API_KEY for regular OpenAI, or\n"
                "- AZURE_OPENAI_ENDPOINT and AZURE_OPENAI_KEY for Azure OpenAI"
            )

    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """Get embeddings using OpenAI text-embedding-3-large model."""
        embeddings = []
        
        # Process in batches to respect API limits
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            
            try:
                response = self.client.embeddings.create(
                    model="text-embedding-3-large",
                    input=batch,
                    encoding_format="float"
                )
                
                batch_embeddings = [data.embedding for data in response.data]
                embeddings.extend(batch_embeddings)
                
                logger.info(f"Processed batch {i//self.batch_size + 1}/{(len(texts) + self.batch_size - 1)//self.batch_size}")
                
            except Exception as e:
                logger.error(f"Error getting embeddings for batch {i//self.batch_size + 1}: {e}")
                # Return zero embeddings for failed batch
                batch_embeddings = [[0.0] * 3072] * len(batch)  # text-embedding-3-large has 3072 dimensions
                embeddings.extend(batch_embeddings)
        
        return np.array(embeddings)

    def _get_mondo_descendants(self, curie: str, adapter) -> List[Tuple[str, str]]:
        """Get all descendants of a MONDO term."""
        all_descendants_data = [
            (adapter.label(child), child)
            for child in adapter.descendants([curie], predicates=[IS_A])
            if child != curie and adapter.label(child) is not None
        ]
        return all_descendants_data

    def _save_synonyms(self, cancers: List[Tuple[str, str]], adapter):
        """Save cancer term synonyms to file."""
        synonyms_dict = {}
        for term, curie in cancers:
            synonyms = adapter.entity_metadata_map(curie).get('oio:hasExactSynonym')
            if synonyms is None:
                synonyms = []
            synonyms.append(term)
            for synonym in synonyms:
                if synonym not in synonyms_dict:
                    synonyms_dict[synonym] = term

        with open(self.cancer_synonyms_file, 'w') as f:
            json.dump(synonyms_dict, f)

    def _get_docs_and_terms(self, cancers: List[Tuple[str, str]], adapter) -> Tuple[List[str], List[str]]:
        """Generate documents and terms for embedding."""
        documents = []
        terms = []

        for term, curie in cancers:
            terms.append(term)
            definition = adapter.definition(curie)
            if definition:
                document = self.chunk_template.format(term=term, definition=definition)
            else:
                document = f"{term} is a type of cancer"
            documents.append(document)

        return documents, terms

    def _fetch_and_save_cancer_terms(self):
        """Fetch cancer terms from MONDO and save embeddings."""
        logger.info("Fetching cancer terms from MONDO ontology...")

        # Initialize MONDO adapter
        adapter = get_adapter("sqlite:obo:mondo")

        # Get cancer descendants
        cancers = self._get_mondo_descendants(self.cancer_mondo_id, adapter)
        logger.info(f"Found {len(cancers)} cancer terms")

        # Save synonyms
        self._save_synonyms(cancers, adapter)

        # Generate documents and terms
        documents, terms = self._get_docs_and_terms(cancers, adapter)

        # Get embeddings
        logger.info("Generating embeddings using OpenAI text-embedding-3-large...")
        embeddings = self._get_embeddings(documents)

        # Save terms and embeddings
        with open(self.cancer_terms_file, 'wb') as f:
            pickle.dump(terms, f)

        with open(self.cancer_embeddings_file, 'wb') as f:
            pickle.dump(embeddings, f)

        logger.info("Cancer terms and embeddings saved successfully")

    def _ensure_cancer_terms_loaded(self):
        """Ensure cancer terms and embeddings are loaded."""
        files_exist = all(
            f.exists() for f in [
                self.cancer_terms_file,
                self.cancer_embeddings_file,
                self.cancer_synonyms_file
            ]
        )

        if not files_exist:
            logger.info("Cancer terms not found, fetching from MONDO...")
            self._fetch_and_save_cancer_terms()

    def standardize(self, term: str, context: Optional[str] = None) -> List[str]:
        """
        Standardize a cancer term using semantic similarity.
        
        Args:
            term: The cancer term to standardize
            context: Optional context for the term
            
        Returns:
            List of standardized cancer terms ranked by similarity
        """
        # Load synonyms dict
        with open(self.cancer_synonyms_file, 'r') as f:
            synonyms_dict = json.load(f)

        # Check for exact synonym match first
        if term in synonyms_dict:
            return [synonyms_dict[term]]

        # Load embeddings and terms
        with open(self.cancer_embeddings_file, 'rb') as f:
            embeddings = pickle.load(f)
            
        with open(self.cancer_terms_file, 'rb') as f:
            terms = pickle.load(f)

        # Create FAISS index
        index = faiss.IndexFlatL2(embeddings.shape[1])
        index.add(embeddings)

        # Generate query
        if context is None:
            query = term
        else:
            query = self.query_template.format(term=term, context=context)

        # Get query embedding
        query_embedding = self._get_embeddings([query])

        # Search for similar terms
        distances, indices = index.search(query_embedding, self.num_output)

        # Return results
        results = [terms[i] for i in indices[0]]
        return results

    def get_standard_cancer_type_subtype(self, 
                                       cancer_type: str, 
                                       cancer_type_context: Optional[str] = None, 
                                       cancer_subtype: Optional[str] = None, 
                                       cancer_subtype_context: Optional[str] = None) -> Union[List[str], Tuple[List[str], List[str]]]:
        """
        Get standardized cancer type and optionally subtype.
        
        Args:
            cancer_type: The cancer type to standardize
            cancer_type_context: Context for the cancer type
            cancer_subtype: Optional cancer subtype to standardize
            cancer_subtype_context: Context for the cancer subtype
            
        Returns:
            List of standardized cancer types, or tuple of (types, subtypes) if subtype provided
        """
        if cancer_subtype is None:
            return self.standardize(cancer_type, cancer_type_context)
        else:
            return (
                self.standardize(cancer_type, cancer_type_context),
                self.standardize(cancer_subtype, cancer_subtype_context)
            )


def main():
    """Command line interface for cancer term standardization."""
    parser = argparse.ArgumentParser(description="Standardize cancer terms using OpenAI embeddings")
    parser.add_argument("term", help="Cancer term to standardize")
    parser.add_argument("--context", help="Context for the cancer term")
    parser.add_argument("--cache-dir", default="cache", help="Directory for cached files")
    parser.add_argument("--num-output", type=int, default=10, help="Number of results to return")
    
    args = parser.parse_args()
    
    # Initialize standardizer
    standardizer = CancerTermStandardizerOpenAI(
        cache_dir=args.cache_dir,
        num_output=args.num_output
    )
    
    # Standardize term
    results = standardizer.standardize(args.term, args.context)
    
    print(f"Standardized terms for '{args.term}':")
    for i, result in enumerate(results, 1):
        print(f"{i}. {result}")


if __name__ == "__main__":
    main()
