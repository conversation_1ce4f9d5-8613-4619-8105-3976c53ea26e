#!/usr/bin/env python3
"""
Cancer Term Standardization Pipeline Integration Script

This script integrates cancer term standardization into the extraction pipeline workflow.
It can be run after extraction is complete and before Excel creation.

Usage:
    python src/run_standardization.py --input-dir results/ --output-dir standardized_results/
    python src/run_standardization.py --input-dir results/ --output-dir standardized_results/ --create-excel
"""

import argparse
import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from cancer_term_standardizer import CancerTermStandardizer
from create_excel_from_extraction import create_excel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function for pipeline integration."""
    parser = argparse.ArgumentParser(
        description="Run cancer term standardization on extraction results"
    )
    parser.add_argument(
        "--input-dir",
        type=str,
        required=True,
        help="Directory containing extraction result JSON files"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        required=True,
        help="Directory to save standardized results"
    )
    parser.add_argument(
        "--create-excel",
        action="store_true",
        help="Create Excel file from standardized results"
    )
    parser.add_argument(
        "--excel-output",
        type=str,
        default="adc_extraction_results_standardized.xlsx",
        help="Output Excel file name (default: adc_extraction_results_standardized.xlsx)"
    )

    args = parser.parse_args()

    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)

    # Validate input directory
    if not input_dir.exists():
        logger.error(f"Input directory does not exist: {input_dir}")
        sys.exit(1)

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Step 1: Run cancer term standardization
        logger.info("Starting cancer term standardization...")
        standardizer = CancerTermStandardizer()
        standardizer.process_directory(input_dir, output_dir)
        logger.info("Cancer term standardization completed successfully")

        # Step 2: Create Excel file if requested
        if args.create_excel:
            logger.info("Creating Excel file from standardized results...")
            excel_path = output_dir / args.excel_output
            create_excel(str(output_dir), str(excel_path))
            logger.info(f"Excel file created: {excel_path}")

        logger.info("Pipeline integration completed successfully")

    except Exception as e:
        logger.error(f"Error in standardization pipeline: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()