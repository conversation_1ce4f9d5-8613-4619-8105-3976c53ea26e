#!/usr/bin/env python3
"""
Complete ADC Extraction Pipeline with Cancer Term Standardization

This script demonstrates the complete workflow:
1. Run extraction pipeline (optional - can use existing results)
2. Run cancer term standardization
3. Create Excel file with standardized results

Usage:
    # Run standardization on existing extraction results
    python src/run_complete_pipeline_with_standardization.py --input-dir results/ --output-dir standardized_results/

    # Run full pipeline including extraction (if needed)
    python src/run_complete_pipeline_with_standardization.py --markdown-dir markdown/ --extraction-dir results/ --output-dir standardized_results/ --run-extraction
"""

import argparse
import sys
import logging
import subprocess
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from cancer_term_standardizer import CancerTermStandardizer
from create_excel_from_extraction import create_excel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_extraction_pipeline(markdown_dir: Path, output_dir: Path):
    """Run the extraction pipeline on markdown files."""
    logger.info("Running extraction pipeline...")

    # Use the existing extraction pipeline
    cmd = [
        sys.executable,
        "src/extraction_pipeline.py",
        "--input-dir", str(markdown_dir),
        "--output-dir", str(output_dir)
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("Extraction pipeline completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Extraction pipeline failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False


def main():
    """Main function for complete pipeline."""
    parser = argparse.ArgumentParser(
        description="Run complete ADC extraction pipeline with cancer term standardization"
    )
    parser.add_argument(
        "--input-dir",
        type=str,
        help="Directory containing extraction result JSON files (required if not running extraction)"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        required=True,
        help="Directory to save standardized results"
    )
    parser.add_argument(
        "--markdown-dir",
        type=str,
        help="Directory containing markdown files (required if running extraction)"
    )
    parser.add_argument(
        "--extraction-dir",
        type=str,
        help="Directory to save extraction results (required if running extraction)"
    )
    parser.add_argument(
        "--run-extraction",
        action="store_true",
        help="Run extraction pipeline before standardization"
    )
    parser.add_argument(
        "--excel-output",
        type=str,
        default="adc_extraction_results_standardized.xlsx",
        help="Output Excel file name (default: adc_extraction_results_standardized.xlsx)"
    )

    args = parser.parse_args()

    # Validate arguments
    if args.run_extraction:
        if not args.markdown_dir or not args.extraction_dir:
            logger.error("--markdown-dir and --extraction-dir are required when using --run-extraction")
            sys.exit(1)
        markdown_dir = Path(args.markdown_dir)
        extraction_dir = Path(args.extraction_dir)
        input_dir = extraction_dir
    else:
        if not args.input_dir:
            logger.error("--input-dir is required when not running extraction")
            sys.exit(1)
        input_dir = Path(args.input_dir)

    output_dir = Path(args.output_dir)

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Step 1: Run extraction pipeline if requested
        if args.run_extraction:
            if not markdown_dir.exists():
                logger.error(f"Markdown directory does not exist: {markdown_dir}")
                sys.exit(1)

            extraction_dir.mkdir(parents=True, exist_ok=True)
            success = run_extraction_pipeline(markdown_dir, extraction_dir)
            if not success:
                logger.error("Extraction pipeline failed")
                sys.exit(1)

        # Validate input directory exists
        if not input_dir.exists():
            logger.error(f"Input directory does not exist: {input_dir}")
            sys.exit(1)

        # Step 2: Run cancer term standardization
        logger.info("Starting cancer term standardization...")
        standardizer = CancerTermStandardizer()
        standardizer.process_directory(input_dir, output_dir)
        logger.info("Cancer term standardization completed successfully")

        # Step 3: Create Excel file
        logger.info("Creating Excel file from standardized results...")
        excel_path = output_dir / args.excel_output
        create_excel(str(output_dir), str(excel_path))
        logger.info(f"Excel file created: {excel_path}")

        logger.info("Complete pipeline with standardization finished successfully")
        logger.info(f"Standardized results: {output_dir}")
        logger.info(f"Excel file: {excel_path}")

    except Exception as e:
        logger.error(f"Error in complete pipeline: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()