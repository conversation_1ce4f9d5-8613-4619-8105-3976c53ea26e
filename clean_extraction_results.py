#!/usr/bin/env python3
"""
Script to clean up redundant entries from JSON extraction result files.
Removes JSON objects that contain ALL of the following three fields with exact values:
- "id": null
- "model_name": null  
- "priority": "prioritize"
"""

import json
import os
import glob
from typing import List, Dict, Any

def should_remove_entry(entry: Dict[str, Any]) -> bool:
    """
    Check if an entry should be removed based on the criteria.
    Returns True if the entry contains ALL three specified field-value pairs.
    """
    return (
        entry.get("id") is None and
        entry.get("model_name") is None and
        entry.get("priority") == "prioritize"
    )

def clean_json_file(file_path: str) -> tuple[int, int]:
    """
    Clean a single JSON file by removing redundant entries.
    Returns (original_count, cleaned_count) tuple.
    """
    try:
        # Read the original file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print(f"Warning: {file_path} does not contain a JSON array. Skipping.")
            return 0, 0
        
        original_count = len(data)
        
        # Filter out entries that match the removal criteria
        cleaned_data = [entry for entry in data if not should_remove_entry(entry)]
        
        cleaned_count = len(cleaned_data)
        removed_count = original_count - cleaned_count
        
        if removed_count > 0:
            # Write the cleaned data back to the file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
            
            print(f"✓ {file_path}: Removed {removed_count} redundant entries ({original_count} → {cleaned_count})")
        else:
            print(f"  {file_path}: No redundant entries found")
        
        return original_count, cleaned_count
        
    except json.JSONDecodeError as e:
        print(f"Error: Failed to parse JSON in {file_path}: {e}")
        return 0, 0
    except Exception as e:
        print(f"Error: Failed to process {file_path}: {e}")
        return 0, 0

def main():
    """Main function to process all JSON files in the extraction results directory."""
    
    # Define the directory path
    results_dir = "regression_evaluation/data/extraction_results"
    
    if not os.path.exists(results_dir):
        print(f"Error: Directory {results_dir} does not exist.")
        return
    
    # Find all JSON files (excluding normalization_log.json as it's likely different format)
    json_files = glob.glob(os.path.join(results_dir, "*_results.json"))
    
    if not json_files:
        print(f"No *_results.json files found in {results_dir}")
        return
    
    print(f"Found {len(json_files)} JSON files to process...")
    print()
    
    total_original = 0
    total_cleaned = 0
    files_modified = 0
    
    # Process each file
    for file_path in sorted(json_files):
        original_count, cleaned_count = clean_json_file(file_path)
        total_original += original_count
        total_cleaned += cleaned_count
        
        if original_count != cleaned_count:
            files_modified += 1
    
    # Summary
    total_removed = total_original - total_cleaned
    print()
    print("=" * 60)
    print("CLEANUP SUMMARY")
    print("=" * 60)
    print(f"Files processed: {len(json_files)}")
    print(f"Files modified: {files_modified}")
    print(f"Total entries before: {total_original}")
    print(f"Total entries after: {total_cleaned}")
    print(f"Total entries removed: {total_removed}")
    print()
    
    if total_removed > 0:
        print("✓ Cleanup completed successfully!")
    else:
        print("No redundant entries found in any files.")

if __name__ == "__main__":
    main()
