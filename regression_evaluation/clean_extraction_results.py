#!/usr/bin/env python3
"""
Script to clean extraction results JSON files by removing problematic null records.

This script identifies and removes records that match the exact pattern:
{
    "id": null,
    "adc_name": null,
    "model_name": null,
    "endpoint_name": null,
    "priority": "prioritize"
}
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, <PERSON><PERSON>


def matches_problematic_pattern(record: Dict[str, Any]) -> bool:
    """
    Check if a record matches the exact problematic pattern.
    
    Args:
        record: Dictionary representing a JSON record
        
    Returns:
        True if the record matches the problematic pattern, False otherwise
    """
    return (
        record.get("id") is None and
        record.get("adc_name") is None and
        record.get("model_name") is None and
        record.get("endpoint_name") is None and
        record.get("priority") == "prioritize"
    )


def clean_json_file(file_path: Path) -> Tuple[int, int]:
    """
    Clean a single JSON file by removing problematic records.
    
    Args:
        file_path: Path to the JSON file to clean
        
    Returns:
        Tuple of (original_count, cleaned_count) records
    """
    try:
        # Load the JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        original_count = 0
        cleaned_data = []
        
        # Handle different JSON structures
        if isinstance(data, list):
            original_count = len(data)
            # Filter out problematic records
            cleaned_data = [record for record in data if not matches_problematic_pattern(record)]
        elif isinstance(data, dict):
            original_count = 1
            # If it's a single record, check if it's problematic
            if not matches_problematic_pattern(data):
                cleaned_data = data
            else:
                cleaned_data = {}
        else:
            print(f"Warning: Unexpected data type in {file_path}: {type(data)}")
            return 0, 0
        
        cleaned_count = len(cleaned_data) if isinstance(cleaned_data, list) else (1 if cleaned_data else 0)
        
        # Only write back if there were changes
        if original_count != cleaned_count:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
            print(f"Cleaned {file_path.name}: {original_count} -> {cleaned_count} records (removed {original_count - cleaned_count})")
        else:
            print(f"No changes needed for {file_path.name}: {original_count} records")
        
        return original_count, cleaned_count
        
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in {file_path}: {e}")
        return 0, 0
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0, 0


def main():
    """Main function to clean all JSON files in the extraction results directory."""
    
    # Define the extraction results directory
    extraction_results_dir = Path("data/extraction_results")
    
    if not extraction_results_dir.exists():
        print(f"Error: Directory {extraction_results_dir} does not exist")
        return
    
    # Find all JSON files (excluding normalization_log.json)
    json_files = [f for f in extraction_results_dir.glob("*.json") if f.name != "normalization_log.json"]
    
    if not json_files:
        print("No JSON files found in the extraction results directory")
        return
    
    print(f"Found {len(json_files)} JSON files to process")
    print("=" * 60)
    
    total_original = 0
    total_cleaned = 0
    files_modified = 0
    
    # Process each JSON file
    for json_file in sorted(json_files):
        original_count, cleaned_count = clean_json_file(json_file)
        total_original += original_count
        total_cleaned += cleaned_count
        
        if original_count != cleaned_count:
            files_modified += 1
    
    print("=" * 60)
    print(f"Summary:")
    print(f"  Files processed: {len(json_files)}")
    print(f"  Files modified: {files_modified}")
    print(f"  Total records before: {total_original}")
    print(f"  Total records after: {total_cleaned}")
    print(f"  Total records removed: {total_original - total_cleaned}")


if __name__ == "__main__":
    main()
