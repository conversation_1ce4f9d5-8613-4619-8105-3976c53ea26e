#!/usr/bin/env python3
"""
Test script for the OpenAI-based cancer term standardizer.
"""

import os
import sys
from dotenv import load_dotenv

# Add src directory to path
sys.path.append('src')

from cancer_term_standardizer_openai import CancerTermStandardizerOpenAI

def test_standardizer():
    """Test the OpenAI cancer term standardizer."""
    
    # Load environment variables
    load_dotenv()
    
    # Check if OpenAI credentials are available
    if not (os.getenv("AZURE_OPENAI_API_KEY") or (os.getenv("AZURE_OPENAI_ENDPOINT") and os.getenv("AZURE_OPENAI_KEY"))):
        print("❌ No OpenAI credentials found!")
        print("Please set either:")
        print("  - AZURE_OPENAI_API_KEY for regular OpenAI")
        print("  - AZURE_OPENAI_ENDPOINT and AZURE_OPENAI_KEY for Azure OpenAI")
        return
    
    print("🚀 Initializing OpenAI Cancer Term Standardizer...")
    
    try:
        # Initialize the standardizer
        standardizer = CancerTermStandardizerOpenAI(
            cache_dir="cache_openai",
            num_output=5,
            batch_size=50
        )
        
        print("✅ Standardizer initialized successfully!")
        
        # Test cases
        test_cases = [
            {
                "term": "Renal Cancer",
                "context": "PDX studies: CTG-0258/0703/0711(OVC) and CTG-1366/1370(RCC) studies were outsourced to Champions Oncology, Inc. Models were established by the subcutaneous inoculation of tumor fragments derived from OVC and RCC, which were maintained in host mice."
            },
            {
                "term": "TNDC",
                "context": "Triple negative ductal carcinoma study"
            },
            {
                "term": "breast cancer",
                "context": None
            },
            {
                "term": "lung adenocarcinoma",
                "context": "Non-small cell lung cancer subtype"
            }
        ]
        
        print("\n🧪 Running test cases...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test Case {i} ---")
            print(f"Term: {test_case['term']}")
            if test_case['context']:
                print(f"Context: {test_case['context'][:100]}...")
            
            try:
                results = standardizer.standardize(
                    test_case['term'], 
                    test_case['context']
                )
                
                print("Standardized terms:")
                for j, result in enumerate(results, 1):
                    print(f"  {j}. {result}")
                    
            except Exception as e:
                print(f"❌ Error processing test case: {e}")
        
        # Test the combined function
        print(f"\n--- Combined Function Test ---")
        try:
            type_results, subtype_results = standardizer.get_standard_cancer_type_subtype(
                cancer_type="breast cancer",
                cancer_type_context="Invasive ductal carcinoma",
                cancer_subtype="triple negative",
                cancer_subtype_context="ER-, PR-, HER2- breast cancer"
            )
            
            print("Cancer type results:")
            for j, result in enumerate(type_results, 1):
                print(f"  {j}. {result}")
                
            print("Cancer subtype results:")
            for j, result in enumerate(subtype_results, 1):
                print(f"  {j}. {result}")
                
        except Exception as e:
            print(f"❌ Error in combined function test: {e}")
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Error initializing standardizer: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_standardizer()
